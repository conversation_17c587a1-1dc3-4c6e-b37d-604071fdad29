/// 文字聊天消息模型
/// 专门用于Stream Channel的文字聊天功能
class ChatMessageModel {
  final String id;
  final String content;
  final int senderId; // agora uid
  final String senderName;
  final String? senderAvatar;
  final int timestamp;
  final ChatMessageType type;
  final String? roomId;
  final Map<String, dynamic>? extra;

  const ChatMessageModel({
    required this.id,
    required this.content,
    required this.senderId,
    required this.senderName,
    this.senderAvatar,
    required this.timestamp,
    required this.type,
    this.roomId,
    this.extra,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'senderId': senderId,
      'senderName': senderName,
      'senderAvatar': senderAvatar,
      'timestamp': timestamp,
      'type': type.name,
      'roomId': roomId,
      'extra': extra,
    };
  }

  factory ChatMessageModel.fromJson(Map<String, dynamic> json) {
    return ChatMessageModel(
      id: json['id'] as String,
      content: json['content'] as String,
      senderId: json['senderId'] as int,
      senderName: json['senderName'] as String,
      senderAvatar: json['senderAvatar'] as String?,
      timestamp: json['timestamp'] as int,
      type: ChatMessageType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ChatMessageType.text,
      ),
      roomId: json['roomId'] as String?,
      extra: json['extra'] as Map<String, dynamic>?,
    );
  }
}

/// 聊天消息类型
enum ChatMessageType {
  /// 普通文字消息
  text,

  /// 系统消息（用户进入、离开等）
  system,

  /// 通知消息
  notification,
}

/// 聊天消息扩展方法
extension ChatMessageModelExtension on ChatMessageModel {
  /// 是否为系统消息
  bool get isSystemMessage => type == ChatMessageType.system;

  /// 是否为普通文字消息
  bool get isTextMessage => type == ChatMessageType.text;

  /// 是否为通知消息
  bool get isNotificationMessage => type == ChatMessageType.notification;

  /// 创建文字消息
  static ChatMessageModel createTextMessage({
    required String content,
    required int senderId,
    required String senderName,
    String? senderAvatar,
    String? roomId,
    Map<String, dynamic>? extra,
  }) {
    return ChatMessageModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      senderId: senderId,
      senderName: senderName,
      senderAvatar: senderAvatar,
      timestamp: DateTime.now().millisecondsSinceEpoch,
      type: ChatMessageType.text,
      roomId: roomId,
      extra: extra,
    );
  }

  /// 创建系统消息
  static ChatMessageModel createSystemMessage({
    required String content,
    String? roomId,
    Map<String, dynamic>? extra,
  }) {
    return ChatMessageModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      senderId: 0, // 系统消息使用0作为发送者ID
      senderName: 'System',
      timestamp: DateTime.now().millisecondsSinceEpoch,
      type: ChatMessageType.system,
      roomId: roomId,
      extra: extra,
    );
  }
}
