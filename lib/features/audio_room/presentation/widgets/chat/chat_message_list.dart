import 'package:flutter/material.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/chat_message/chat_message_model.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/chat_message_provider.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/chat/chat_message_item.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// 聊天消息列表组件
class ChatMessageList extends ConsumerStatefulWidget {
  final double? height;
  final EdgeInsets? padding;
  final bool showSystemMessages;
  final bool autoScroll;
  final ChatMessageListController? controller;

  const ChatMessageList({
    super.key,
    this.height,
    this.padding,
    this.showSystemMessages = true,
    this.autoScroll = true,
    this.controller,
  });

  @override
  ConsumerState<ChatMessageList> createState() => _ChatMessageListState();
}

/// 聊天消息列表控制器
class ChatMessageListController {
  _ChatMessageListState? _state;

  void _attach(_ChatMessageListState state) {
    _state = state;
  }

  void _detach() {
    _state = null;
  }

  /// 滚动到底部
  void scrollToBottom() {
    _state?.scrollToBottom();
  }
}

class _ChatMessageListState extends ConsumerState<ChatMessageList> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    widget.controller?._attach(this);
  }

  @override
  void dispose() {
    widget.controller?._detach();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final messages = ref.watch(chatMessageProvider);

    // 过滤消息
    final filteredMessages = widget.showSystemMessages
        ? messages
        : messages.where((m) => m.type == ChatMessageType.text).toList();

    // 监听消息变化，自动滚动到底部
    ref.listen<List<ChatMessageModel>>(chatMessageProvider, (previous, next) {
      if (widget.autoScroll &&
          next.isNotEmpty &&
          previous != null &&
          next.length > previous.length) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToBottom();
        });
      }
    });

    return Container(
      height: widget.height,
      padding: widget.padding ?? const EdgeInsets.all(8.0),
      child: filteredMessages.isEmpty
          ? _buildEmptyState()
          : ListView.builder(
              controller: _scrollController,
              itemCount: filteredMessages.length,
              itemBuilder: (context, index) {
                final message = filteredMessages[index];
                return ChatMessageItem(
                  message: message,
                  showAvatar: message.type == ChatMessageType.text,
                  showTimestamp: _shouldShowTimestamp(filteredMessages, index),
                );
              },
            ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 48,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            '暂无消息',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 16,
            ),
          ),
          SizedBox(height: 8),
          Text(
            '开始聊天吧！',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  /// 判断是否显示时间戳
  bool _shouldShowTimestamp(List<ChatMessageModel> messages, int index) {
    if (index == 0) return true;

    final currentMessage = messages[index];
    final previousMessage = messages[index - 1];

    // 如果两条消息间隔超过5分钟，显示时间戳
    final timeDiff = currentMessage.timestamp - previousMessage.timestamp;
    return timeDiff > 5 * 60 * 1000; // 5分钟
  }

  /// 滚动到底部
  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  /// 手动滚动到底部（公开方法）
  void scrollToBottom() {
    _scrollToBottom();
  }
}
