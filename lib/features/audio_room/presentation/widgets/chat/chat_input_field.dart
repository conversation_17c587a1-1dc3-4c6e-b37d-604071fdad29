import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/utils/app_screen_utils.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 聊天输入框组件
class ChatInputField extends ConsumerStatefulWidget {
  final Function(String message) onSendMessage;
  final String? hintText;
  final int maxLength;
  final bool enabled;
  final EdgeInsets? padding;

  const ChatInputField({
    super.key,
    required this.onSendMessage,
    this.hintText,
    this.maxLength = 200,
    this.enabled = true,
    this.padding,
  });

  @override
  ConsumerState<ChatInputField> createState() => _ChatInputFieldState();
}

class _ChatInputFieldState extends ConsumerState<ChatInputField> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  bool _isComposing = false;

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF262626),
        borderRadius: BorderRadius.circular(20.r),
        border: Border.all(
          color: Colors.grey.withValues(alpha: 0.2),
          width: 1.0,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildInputField(),
          ),
          SizedBox(width: AppScreenUtils.space8),
          _buildSendButton(),
        ],
      ),
    );
  }

  /// 构建输入框
  Widget _buildInputField() {
    return TextField(
      controller: _controller,
      focusNode: _focusNode,
      enabled: widget.enabled,
      maxLength: widget.maxLength,
      maxLines: 1,
      minLines: 1,
      textInputAction: TextInputAction.send,
      decoration: InputDecoration(
        hintText: widget.hintText ?? '说点什么...',
        hintStyle: TextStyle(
          color: Colors.grey.withValues(alpha: 0.7),
          fontSize: 14.sp,
        ),
        border: InputBorder.none,
        contentPadding: EdgeInsets.symmetric(
          horizontal: 16.w,
          vertical: 10.h,
        ),
        counterText: '', // 隐藏字符计数
      ),
      style: TextStyle(
        fontSize: 14.sp,
        color: Colors.white,
      ),
      onChanged: (text) {
        setState(() {
          _isComposing = text.trim().isNotEmpty;
        });
      },
      onSubmitted: (_) => _handleSendMessage(),
    );
  }

  /// 构建发送按钮
  Widget _buildSendButton() {
    return GestureDetector(
      onTap: _isComposing && widget.enabled ? _handleSendMessage : null,
      child: SizedBox(
        width: 40.w,
        height: 40.w,
        child: Icon(
          Icons.send,
          color: _isComposing && widget.enabled
              ? context.theme.colorScheme.primary
              : Colors.grey.withValues(alpha: 0.7),
          size: 18.w,
        ),
      ),
    );
  }

  /// 处理发送消息
  void _handleSendMessage() {
    final message = _controller.text.trim();
    if (message.isEmpty || !widget.enabled) return;

    // 发送消息
    widget.onSendMessage(message);

    // 清空输入框
    _controller.clear();
    setState(() {
      _isComposing = false;
    });

    // 保持焦点
    _focusNode.requestFocus();
  }

  /// 清空输入框
  void clear() {
    _controller.clear();
    setState(() {
      _isComposing = false;
    });
  }

  /// 设置文本
  void setText(String text) {
    _controller.text = text;
    setState(() {
      _isComposing = text.trim().isNotEmpty;
    });
  }

  /// 获取当前文本
  String get text => _controller.text;

  /// 获取焦点
  void requestFocus() {
    _focusNode.requestFocus();
  }

  /// 失去焦点
  void unfocus() {
    _focusNode.unfocus();
  }
}
