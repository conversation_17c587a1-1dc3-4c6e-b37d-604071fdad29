import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/audio_room_screen/unified_message_list.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 音频房间聊天区域组件
/// 整合了传统房间消息和新的文字聊天功能
class AudioRoomChatSection extends ConsumerStatefulWidget {
  const AudioRoomChatSection({super.key});

  @override
  ConsumerState<AudioRoomChatSection> createState() =>
      _AudioRoomChatSectionState();
}

class _AudioRoomChatSectionState extends ConsumerState<AudioRoomChatSection> {
  @override
  void initState() {
    super.initState();
    LogUtils.d('AudioRoomChatSection初始化', tag: 'AudioRoomChatSection');
  }

  @override
  Widget build(BuildContext context) {
    return _buildUnifiedMessageList();
  }

  /// 构建统一消息列表
  Widget _buildUnifiedMessageList() {
    return Container(
      margin: EdgeInsets.only(top: 8.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: const UnifiedMessageList(),
    );
  }
}
