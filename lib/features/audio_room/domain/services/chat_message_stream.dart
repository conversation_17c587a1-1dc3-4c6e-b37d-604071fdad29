import 'dart:async';

import 'package:flutter_audio_room/features/audio_room/data/model/chat_message/chat_message_model.dart';

/// 聊天消息流服务
/// 专门处理文字聊天消息的流管理
class ChatMessageStream {
  /// 消息流控制器
  final StreamController<ChatMessageModel> _controller = 
      StreamController<ChatMessageModel>.broadcast();
  
  /// 获取消息流
  Stream<ChatMessageModel> get stream => _controller.stream;
  
  /// 获取消息流控制器
  StreamController<ChatMessageModel> get controller => _controller;
  
  /// 发送消息到流
  void add(ChatMessageModel message) {
    if (!_controller.isClosed) {
      _controller.add(message);
    }
  }
  
  /// 获取特定类型的消息流
  Stream<ChatMessageModel> getByType(ChatMessageType type) {
    return stream.where((message) => message.type == type);
  }
  
  /// 获取文字消息流
  Stream<ChatMessageModel> get textMessages => 
      getByType(ChatMessageType.text);
  
  /// 获取系统消息流
  Stream<ChatMessageModel> get systemMessages => 
      getByType(ChatMessageType.system);
  
  /// 获取通知消息流
  Stream<ChatMessageModel> get notificationMessages => 
      getByType(ChatMessageType.notification);
  
  /// 关闭流
  void dispose() {
    if (!_controller.isClosed) {
      _controller.close();
    }
  }
  
  /// 检查流是否已关闭
  bool get isClosed => _controller.isClosed;
}
